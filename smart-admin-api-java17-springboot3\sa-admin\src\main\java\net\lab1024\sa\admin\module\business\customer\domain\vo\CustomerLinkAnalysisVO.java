package net.lab1024.sa.admin.module.business.customer.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 链接视角 - 整体分析结果VO
 *
 * <AUTHOR>
 * @Date 2025-01-18 10:15:00
 * @Copyright 1.0
 */
@Data
@Schema(description = "链接分析统计 VO")
public class CustomerLinkAnalysisVO {

    @Schema(description = "订单数量")
    private Long orderCount;

    @Schema(description = "付款人数")
    private Long paymentUserCount;

    @Schema(description = "复购人数")
    private Long repurchaseUserCount;

    @Schema(description = "复购率")
    private BigDecimal repurchaseRate;

    @Schema(description = "平均复购周期")
    private BigDecimal avgRepurchaseCycle;

    @Schema(description = "最小复购间隔")
    private Integer minRepurchaseInterval;

    @Schema(description = "最大复购间隔")
    private Integer maxRepurchaseInterval;

    @Schema(description = "平均复购间隔")
    private BigDecimal avgRepurchaseInterval;

    @Schema(description = "回购日期复购人数")
    private Long repurchaseDateUserCount;

    @Schema(description = "回购日期复购率")
    private BigDecimal repurchaseDateRate;
}