# 项目上下文信息

- SmartAdmin项目结构：
1. 项目类型：前后端分离的企业级管理系统
2. 前端：smart-admin-web-javascript (Vue3 + Ant Design Vue + Vite)
3. 后端：smart-admin-api-java17-springboot3 (Java17 + SpringBoot3 + Sa-Token + Mybatis-Plus)
4. 移动端：smart-app (UniApp + Vue3)
5. 数据库：sql目录包含数据库脚本
6. 技术栈：满足三级等保要求，支持加解密、数据脱敏等安全功能
- 客户管理模块详细结构：
1. 前端页面：customer-view.vue（主页面）、customer-link.vue（客户关联）
2. 前端组件：customer-view-detail-modal.vue、customer-view-statistics-modal.vue、customer-record-modal.vue等
3. 前端API：customer-view-api.js、customer-order-api.js、customer-link-api.js、customer-record-api.js
4. 后端Controller：CustomerViewController、CustomerOrderController、CustomerLinkController、CustomerRecordController
5. 后端Service：CustomerViewService、CustomerOrderService、CustomerLinkService、CustomerRecordService、LirunJdbcService
6. 数据实体：CustomerEntity、CustomerLinkEntity、CustomerRecordEntity、PlatformProductEntity
7. 权限标识：customer:view:query、customer:view:detail、customer:view:export、customer:record:manage
- 链接视角功能详细结构：前端页面customer-link.vue，后端Controller/Service/DAO，数据库表lirun.订单明细和smart_admin_v3.t_menu，菜单ID 3074，权限包括查询、统计分析、下载明细等
- SQL文件夹已完成重新整理：1)创建了4个分类目录：01-database-init(数据库初始化)、02-version-updates(版本更新)、03-feature-scripts(功能脚本)、04-deprecated(已废弃)；2)将customer_classification_enhancement.sql移至废弃目录，因为已改为实时计算方案；3)整理了版本更新脚本、功能脚本和代码生成模板；4)创建了详细的README文档说明各脚本用途和使用方法
- 用户希望为员工管理系统添加高级安全验证功能，当执行敏感操作（禁用/启用员工、重置密码）时需要向超级管理员邮箱发送验证。需要分析技术可行性、架构影响和实现方案。
