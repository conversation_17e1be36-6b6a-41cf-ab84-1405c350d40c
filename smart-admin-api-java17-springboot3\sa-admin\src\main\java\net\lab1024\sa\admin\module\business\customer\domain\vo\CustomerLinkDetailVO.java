package net.lab1024.sa.admin.module.business.customer.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 链接视角 - 复购明细VO
 *
 * <AUTHOR>
 * @Date 2025-01-18 10:10:00
 * @Copyright 1.0
 */
@Data
public class CustomerLinkDetailVO {
    
    @Schema(description = "复购次数")
    private String repurchaseTimes;

    @Schema(description = "复购人数")
    private Long repurchaseCustomers;

    @Schema(description = "复购件数")
    private Long repurchaseQuantity;

    @Schema(description = "复购金额")
    private BigDecimal repurchaseAmount;

    @Schema(description = "平均复购周期天数")
    private BigDecimal avgRepurchaseCycleDays;
} 